import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';
import { MySQLModule } from './database/mysql/mysql.module';
import { RedisModule } from './database/redis/redis.module';
import { PostgreSQLModule } from './database/postgresql/postgresql.module';
import { MinioModule } from './minio/minio.module';
import { BackupModule } from './backup/backup.module';
import { MediaModule } from './media/media.module';
import { StorageController } from './storage.controller';
import { StorageService } from './storage.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 调度模块
    ScheduleModule.forRoot(),

    // 队列模块
    BullModule.forRootAsync({
      useFactory: () => ({
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT) || 6379,
          password: process.env.REDIS_PASSWORD,
          db: parseInt(process.env.REDIS_QUEUE_DB) || 1,
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
    }),

    // 数据库模块
    MySQLModule,
    RedisModule,
    PostgreSQLModule,

    // 存储模块
    MinioModule,

    // 备份模块
    BackupModule,

    // 媒体处理模块
    MediaModule,
  ],
  controllers: [StorageController],
  providers: [StorageService],
  exports: [
    StorageService,
    MySQLModule,
    RedisModule,
    PostgreSQLModule,
    MinioModule,
    BackupModule,
    MediaModule,
  ],
})
export class StorageModule {}
