version: '3.8'

services:
  # API网关
  gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - API_SERVICE_URL=http://api:3030
      - AUTH_SERVICE_URL=http://auth:3031
      - INSTANCE_SERVICE_URL=http://instance:3032
      - TASK_SERVICE_URL=http://task:3033
    depends_on:
      - api
      - auth
      - instance
      - task
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 核心API服务
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "3030:3030"
    environment:
      - NODE_ENV=production
      - PORT=3030
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=dl_engine
      - DB_PASSWORD=dl_engine_password
      - DB_DATABASE=dl_engine
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 认证服务
  auth:
    build:
      context: ./auth
      dockerfile: Dockerfile
    ports:
      - "3031:3031"
    environment:
      - NODE_ENV=production
      - PORT=3031
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=dl_engine
      - DB_PASSWORD=dl_engine_password
      - DB_DATABASE=dl_engine_auth
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key
      - SMS_PROVIDER=aliyun
      - SMS_ACCESS_KEY=your-sms-access-key
      - SMS_SECRET_KEY=your-sms-secret-key
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 实例服务
  instance:
    build:
      context: ./instance
      dockerfile: Dockerfile
    ports:
      - "3032:3032"
    environment:
      - NODE_ENV=production
      - PORT=3032
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MAX_INSTANCES=100
      - INSTANCE_TIMEOUT=3600
    depends_on:
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # 任务服务
  task:
    build:
      context: ./task
      dockerfile: Dockerfile
    ports:
      - "3033:3033"
    environment:
      - NODE_ENV=production
      - PORT=3033
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - QUEUE_CONCURRENCY=10
    depends_on:
      - redis
    networks:
      - dl-engine-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=dl_engine
      - MYSQL_USER=dl_engine
      - MYSQL_PASSWORD=dl_engine_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - dl-engine-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - gateway
    networks:
      - dl-engine-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  dl-engine-network:
    driver: bridge
