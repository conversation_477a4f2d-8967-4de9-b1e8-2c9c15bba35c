{"name": "@dl-engine/server-storage", "version": "1.0.0", "description": "DL-Engine 存储服务", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/schedule": "^4.0.0", "typeorm": "^0.3.0", "mysql2": "^3.0.0", "redis": "^4.0.0", "pg": "^8.0.0", "minio": "^7.0.0", "aws-sdk": "^2.1000.0", "node-cron": "^3.0.0", "archiver": "^6.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/pg": "^8.0.0", "@types/archiver": "^6.0.0", "typescript": "^5.0.0", "ts-node": "^10.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "eslint": "^8.0.0"}}